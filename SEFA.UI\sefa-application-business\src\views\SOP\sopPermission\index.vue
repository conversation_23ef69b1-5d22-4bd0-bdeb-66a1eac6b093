<template>
    <div class="root usemystyle">
        <div class="root-layout" v-loading="initLoading">
            <split-pane
                :min-percent="15"
                :max-percent="40"
                :default-percent="20"
                split="vertical">
                <template slot="pane1">
                    <div class="root-left">
                        <div class="tree-toolbar">
                            <el-button-group>
                                <el-button
                                    size="small"
                                    icon="el-icon-refresh"
                                    @click="getDirTree">刷新</el-button>
                                <el-button
                                    size="small"
                                    @click="expandAll">展开</el-button>
                                <el-button
                                    size="small"
                                    @click="collapseAll">收起</el-button>
                            </el-button-group>
                        </div>
                        <el-tree
                            ref="tree"
                            :data="treeData"
                            :props="defaultProps"
                            highlight-current
                            @node-click="handleNodeClick"
                            v-loading="treeLoading">
                            <span class="custom-tree-node" slot-scope="{ node }">
                                <div style="line-height: 22px;">
                                    <div class="tree-title">{{ node.data.name }}</div>
                                </div>
                            </span>
                        </el-tree>
                    </div>
                </template>
                <template slot="pane2">
                    <div class="root-right">
                        <div class="InventorySearchBox">
                            <div class="search-form">
                                <el-form size="small" :inline="true" ref="form" :model="searchForm" @submit.native.prevent>
                                    <div class="form-content">
                                        <div class="search-area">
                                            <div class="search-row">
                                                <el-form-item label="目录名称" prop="dirName" label-width="100px">
                                                    <el-input v-model="searchForm.dirName" placeholder="输入目录名称" clearable size="small" style="width: 200px;"></el-input>
                                                </el-form-item>
                                                <el-form-item label="目录编码" prop="dirCode" label-width="100px">
                                                    <el-input v-model="searchForm.dirCode" placeholder="输入目录编码" clearable size="small" style="width: 150px;"></el-input>
                                                </el-form-item>
                                                <div class="action-buttons">
                                                    <el-button type="primary" icon="el-icon-search" @click="getSearchBtn" size="small">查询</el-button>
                                                    <el-button icon="el-icon-refresh" @click="resetForm" size="small">重置</el-button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </el-form>
                            </div>
                        </div>
                        <div class="root-main">
                            <!-- 数据统计信息 -->
                            <div class="data-summary" v-if="tableData.length > 0">
                                <div class="summary-item">
                                    <span class="summary-label">当前目录：</span>
                                    <span class="summary-value">{{ getCurrentDirName() }}</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">总计角色：</span>
                                    <span class="summary-value">{{ total }}</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">已配置权限：</span>
                                    <span class="summary-value">{{ getConfiguredCount() }}</span>
                                </div>
                                <div class="summary-item">
                                    <span class="summary-label">未配置权限：</span>
                                    <span class="summary-value">{{ total - getConfiguredCount() }}</span>
                                </div>
                            </div>

                            <el-table
                                class="mt-3"
                                :height="mainH"
                                border
                                :data="tableData"
                                style="width: 100%; border-radius: 4px;"
                                v-loading="tableLoading"
                                :empty-text="'暂无数据'"
                                ref="permissionTable">

                                <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>

                                <el-table-column prop="RoleName" :label="$t('SopPermission.table.RoleName')" min-width="150" show-overflow-tooltip></el-table-column>

                                <el-table-column :label="$t('SopPermission.table.Preview')" width="80" align="center">
                                    <template slot-scope="scope">
                                        <el-checkbox :value="scope.row.Preview" @input="handlePermissionChange(scope.row, 'Preview', $event)"></el-checkbox>
                                    </template>
                                </el-table-column>

                                <el-table-column :label="$t('SopPermission.table.Download')" width="80" align="center">
                                    <template slot-scope="scope">
                                        <el-checkbox :value="scope.row.Download" @input="handlePermissionChange(scope.row, 'Download', $event)"></el-checkbox>
                                    </template>
                                </el-table-column>

                                <el-table-column :label="$t('SopPermission.table.Search')" width="80" align="center">
                                    <template slot-scope="scope">
                                        <el-checkbox :value="scope.row.Search" @input="handlePermissionChange(scope.row, 'Search', $event)"></el-checkbox>
                                    </template>
                                </el-table-column>

                                <el-table-column :label="$t('SopPermission.table.Upload')" width="80" align="center">
                                    <template slot-scope="scope">
                                        <el-checkbox :value="scope.row.Upload" @input="handlePermissionChange(scope.row, 'Upload', $event)"></el-checkbox>
                                    </template>
                                </el-table-column>

                                <el-table-column :label="$t('SopPermission.table.Delete')" width="80" align="center">
                                    <template slot-scope="scope">
                                        <el-checkbox :value="scope.row.Delete" @input="handlePermissionChange(scope.row, 'Delete', $event)"></el-checkbox>
                                    </template>
                                </el-table-column>

                                <el-table-column :label="$t('SopPermission.table.Operation')" width="160" align="center">
                                    <template slot-scope="scope">
                                        <div style="display: flex; justify-content: center; gap: 4px;">
                                            <el-button size="mini" type="text" @click="selectAll(scope.row)">
                                                {{ $t('SopPermission.table.SelectAll') }}
                                            </el-button>
                                            <el-button size="mini" type="text" @click="cancelAll(scope.row)">
                                                {{ $t('SopPermission.table.CancelAll') }}
                                            </el-button>
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>

                    </div>
                </template>
            </split-pane>
        </div>
    </div>
</template>

<script>
import '@/views/Inventory/mystyle.scss';

import SplitPane from '../components/split-pane';
import { getSopPermissionList, saveSopPermissionForm } from '@/api/SOP/sopPermission';
import { getSopDirTree } from '@/api/SOP/sopDir';

export default {
    name: 'index.vue',
    components: {
        SplitPane
    },
    data() {
        return {
            // 初始化加载状态
            initLoading: false,
            // 树相关数据
            treeData: [],
            treeLoading: false,
            selectedDir: null,
            defaultProps: {
                children: 'children',
                label: 'name'
            },
            // 表格相关数据
            searchForm: {
                dirName: undefined,
                dirCode: undefined,
                targetId: undefined,
                targetType: undefined,
                grantType: undefined,
                grantId: undefined,
                permLevel: undefined
            },
            total: 0,
            tableData: [],
            tableLoading: false,
            mainH: 0
        };
    },
    async mounted() {
        try {
            this.initLoading = true;
            await this.getDirTree();
            // 页面加载时默认请求根目录权限数据
            this.searchForm.targetId = this.treeData[0].id; // 设置默认查询根目录
            await this.getTableData();
            this.$nextTick(() => {
                this.mainH = this.$webHeight(
                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,
                    document.getElementsByClassName('root')[0]?.clientHeight || 0
                );
            });
            window.onresize = () => {
                this.mainH = this.$webHeight(
                    document.getElementsByClassName('root-head')[0]?.clientHeight || 0,
                    document.getElementsByClassName('root')[0]?.clientHeight || 0
                );
            };
        } catch (err) {
            console.error('页面初始化失败:', err);
            this.$message.error('页面初始化失败，请刷新重试');
        } finally {
            this.initLoading = false;
        }
    },
    methods: {
        // 获取目录树
        async getDirTree() {
            this.treeLoading = true;
            try {
                const res = await getSopDirTree();
                if (res.success) {
                    this.treeData = res.response || [];
                } else {
                    this.$message.error(res.msg || '获取目录树失败');
                }
            } catch (err) {
                console.error('获取目录树失败:', err);
                this.$message.error('获取目录树失败');
                throw err;
            } finally {
                this.treeLoading = false;
            }
        },

        // 处理树节点点击
        handleNodeClick(data) {
            this.selectedDir = data;
            // 保留查询条件，更新目标目录
            const { dirName, dirCode } = this.searchForm;
            this.searchForm = {
                dirName,
                dirCode,
                targetId: data.id,
                targetType: 1, // 1表示目录
                grantType: undefined,
                grantId: undefined, // 使用目录ID或编码作为grantId
                permLevel: undefined
            };
            this.getTableData();
        },

        // 展开所有节点
        expandAll() {
            const nodes = this.$refs.tree.store._getAllNodes();
            nodes.forEach(node => {
                this.expandNodes(node, true);
            });
        },

        // 收起所有节点
        collapseAll() {
            const nodes = this.$refs.tree.store._getAllNodes();
            nodes.forEach(node => {
                this.expandNodes(node, false);
            });
        },

        // 树节点展开关闭
        expandNodes(node, type) {
            node.expanded = type;
            for (let i = 0; i < node.childNodes.length; i++) {
                node.childNodes[i].expanded = type;
                if (node.childNodes[i].childNodes.length > 0) {
                    this.expandNodes(node.childNodes[i], type);
                }
            }
        },

        // 处理权限变更
        async handlePermissionChange(row, permission, value) {
            console.log('权限变更:', permission, value);
            this.$set(row, permission, value);
            await this.updatePermLevel(row);
        },

        // 权限级别更新方法
        async updatePermLevel(row) {
            let permissions = [];
            if (row.Preview) permissions.push('1');    // 预览 (1)
            if (row.Download) permissions.push('2');   // 下载 (2)
            if (row.Search) permissions.push('3');     // 检索 (3)
            if (row.Upload) permissions.push('4');     // 上传 (4)
            if (row.Delete) permissions.push('8');     // 删除 (8)

            row.PermLevel = permissions.join(',');
            console.log('权限更新:', row.RoleName, '权限级别:', row.PermLevel);

            // 调用API保存权限变更
            await this.savePermission(row);
        },

        // 保存权限到后端
        async savePermission(row) {
            try {
                const saveData = {
                    ID: row.ID,
                    TargetId: row.TargetId || this.searchForm.targetId,
                    TargetType: row.TargetType || this.searchForm.targetType || 1,
                    GrantType: row.GrantType || this.searchForm.grantType || 1,
                    GrantId: row.GrantId || this.searchForm.grantId,
                    RoleName: row.RoleName,
                    PermLevel: row.PermLevel,
                    CreateDate: row.CreateDate,
                    CreateUserId: row.CreateUserId,
                    ModifyDate: new Date().toISOString(),
                    ModifyUserId: this.$store.state.user.userInfo?.userId || 'system'
                };

                console.log('保存权限数据:', saveData);
                const res = await saveSopPermissionForm(saveData);

                if (res.success) {
                    this.$message.success(`${row.RoleName} 权限保存成功`);
                    // 保存成功后刷新表格数据
                    await this.getTableData();
                } else {
                    this.$message.error(res.msg || '权限保存失败');
                }
            } catch (err) {
                console.error('保存权限失败:', err);
                this.$message.error('权限保存失败，请重试');
            }
        },

        // 全选权限
        async selectAll(row) {
            console.log('全选权限:', row.RoleName);
            this.$set(row, 'Preview', true);
            this.$set(row, 'Download', true);
            this.$set(row, 'Search', true);
            this.$set(row, 'Upload', true);
            this.$set(row, 'Delete', true);
            await this.updatePermLevel(row);
        },

        // 取消全选
        async cancelAll(row) {
            console.log('取消全选:', row.RoleName);
            this.$set(row, 'Preview', false);
            this.$set(row, 'Download', false);
            this.$set(row, 'Search', false);
            this.$set(row, 'Upload', false);
            this.$set(row, 'Delete', false);
            await this.updatePermLevel(row);
        },

        // 解析权限级别字符串
        parsePermLevel(permLevel, row) {
            // 使用$set确保响应式
            this.$set(row, 'Preview', false);
            this.$set(row, 'Download', false);
            this.$set(row, 'Search', false);
            this.$set(row, 'Upload', false);
            this.$set(row, 'Delete', false);

            // 处理 null、undefined 或空字符串的情况
            if (!permLevel || permLevel === 'null' || permLevel === '') {
                return;
            }

            // 将权限字符串按逗号分割成数组
            const permissions = permLevel.toString().split(',').map(p => p.trim()).filter(p => p && p !== 'null');

            // 根据权限数组设置对应的权限状态
            permissions.forEach(perm => {
                switch (perm) {
                    case '1':
                        this.$set(row, 'Preview', true);     // 预览
                        break;
                    case '2':
                        this.$set(row, 'Download', true);    // 下载
                        break;
                    case '3':
                        this.$set(row, 'Search', true);      // 检索
                        break;
                    case '4':
                        this.$set(row, 'Upload', true);      // 上传
                        break;
                    case '8':
                        this.$set(row, 'Delete', true);      // 删除
                        break;
                }
            });
        },
        getSearchBtn() {
            this.getTableData();
        },

        // 重置查询表单
        resetForm() {
            this.searchForm = {
                dirName: undefined,
                dirCode: undefined,
                targetId: this.searchForm.targetId, // 保留当前选中的目录
                targetType: this.searchForm.targetType,
                grantType: undefined,
                grantId: this.searchForm.grantId, // 保留当前的grantId
                permLevel: undefined
            };
            this.getTableData();
        },

        // 获取已配置权限的角色数量
        getConfiguredCount() {
            return this.tableData.filter(row => {
                return row.Preview || row.Download || row.Search || row.Upload || row.Delete;
            }).length;
        },

        // 获取当前目录名称
        getCurrentDirName() {
            if (this.selectedDir) {
                return this.selectedDir.name || this.selectedDir.dirName || '未知目录';
            }
            return '根目录';
        },



        async getTableData() {
            this.tableLoading = true;
            try {
                const res = await getSopPermissionList(this.searchForm);
                console.log('API返回数据:', res);

                // 处理后端返回的数据结构
                let data = [];

                if (res && res.success !== false) {
                    if (res.response && Array.isArray(res.response.data)) {
                        // 如果数据在 response.data 中
                        data = res.response.data;
                        this.total = res.response.dataCount || data.length;
                    } else if (res.response && Array.isArray(res.response)) {
                        // 如果数据直接在 response 中
                        data = res.response;
                        this.total = data.length;
                    } else if (Array.isArray(res)) {
                        // 如果数据直接是数组
                        data = res;
                        this.total = data.length;
                    } else if (res.success && res.response) {
                        // 处理其他可能的数据结构
                        data = Array.isArray(res.response) ? res.response : [];
                        this.total = data.length;
                    }

                    this.tableData = data;
                    console.log('处理后的表格数据:', this.tableData);

                    // 解析每行的权限级别
                    this.tableData.forEach(row => {
                        this.parsePermLevel(row.PermLevel, row);
                    });
                } else {
                    console.log('API调用失败或返回错误:', res);
                    this.$message.error(res?.msg || '获取权限数据失败');
                    this.tableData = [];
                    this.total = 0;
                }
            } catch (err) {
                console.error('获取权限数据失败:', err);
                this.$message.error('获取权限数据失败，请检查网络连接');
                this.tableData = [];
                this.total = 0;
            } finally {
                this.tableLoading = false;
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.root-layout {
  height: calc(100% - 60px);
}

.root-left {
  height: 100%;
  padding: 10px;
  overflow: auto;
  background-color: #f5f7fa;
  border-radius: 4px;

  .tree-toolbar {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    width: 100%;
    .tree-title {
      font-weight: 500;
    }
  }

  .tree-node-actions {
    display: none;
  }

  .el-tree-node__content:hover {
    .tree-node-actions {
      display: inline-block;
    }
  }
}

.root-right {
  padding: 0 12px;
  height: 100%;
  overflow: auto;

  .InventorySearchBox {
    background: #fff;
    padding: 8px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 8px;

    .search-form {
      :deep(.el-form) {
        .el-form-item--small.el-form-item {
          margin-bottom: 0;
        }
      }

      .form-content {
        padding: 4px;

        .search-area {
          .search-row {
            display: flex;
            align-items: center;
            gap: 4px;

            .el-form-item {
              margin: 0;
              flex: none;

              .el-form-item__label {
                padding-right: 4px;
                line-height: 26px;
                font-size: 12px;
              }

              .el-form-item__content {
                line-height: 26px;

                .el-input,
                .el-select {
                  :deep(.el-input__inner) {
                    height: 26px;
                    line-height: 26px;
                    padding: 0 8px;
                    font-size: 12px;
                  }

                  :deep(.el-input-group__append) {
                    padding: 0;
                    .el-button {
                      padding: 0 10px;
                      height: 26px;
                      border: none;
                    }
                  }
                }
              }
            }

            .action-buttons {
              display: flex;
              gap: 4px;
              margin-left: 8px;

              .el-button {
                height: 26px;
                padding: 0 10px;
                font-size: 12px;

                [class^="el-icon-"] {
                  margin-right: 3px;
                  font-size: 12px;
                }
              }
            }
          }
        }
      }
    }
  }

  .root-head {
    background: #fff;
    padding: 8px;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 8px;

    .selected-dir-info {
      padding: 4px;

      span {
        font-size: 14px;
        color: #606266;
        font-weight: 500;
      }
    }
  }

  .root-main {
    margin-top: 12px;

    .data-summary {
      background: #fff;
      border-radius: 4px;
      padding: 12px;
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
      border: 1px solid #ebeef5;

      .summary-item {
        text-align: center;
        flex: 1;

        &:first-child {
          .summary-value {
            color: #409eff;
          }
        }

        .summary-label {
          display: block;
          font-size: 12px;
          color: #606266;
          margin-bottom: 4px;
        }

        .summary-value {
          display: block;
          font-size: 16px;
          font-weight: bold;
          color: #303133;
        }
      }
    }

    .el-table {
      border-radius: 4px;
      overflow: hidden;
    }
  }

  .root-footer {
    margin-top: 12px;
    padding: 8px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  }
}
</style>