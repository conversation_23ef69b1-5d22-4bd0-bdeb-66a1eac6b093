{"remainingRequest": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDoc\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\src\\views\\SOP\\sopDoc\\index.vue", "mtime": 1750299534800}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\babel.config.js", "mtime": 1742799047338}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1743379024462}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1743379022734}, {"path": "C:\\work\\syngentagroup\\SEFA_XZD\\SEFA.UI\\sefa-application-business\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1743379013975}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAqKA;AACA;AACA;AACA;AACA,SACAA,SADA,EACAC,aADA,EACAC,cADA,QAEA,kBAFA;AAGA;AACA;AACA;AAEA;EACAC,iBADA;EAEAC;IACAC,UADA;IAEAC,gBAFA;IAGAC;EAHA,CAFA;;EAOAC;IACA;MACAC;QACAC,YADA;QAEAC,YAFA;QAGAC,SAHA;QAIAC,WAJA;QAKAC,WALA;QAMAC,aANA;QAOAC;MAPA,CADA;MAUAC,QAVA;MAWAC,aAXA;MAYAC,iCAZA;MAaAC,aAbA;MAcAC,mBAdA;MAeAC,kBAfA;MAgBAC,kBAhBA;MAiBAC,eAjBA;MAkBAC,QAlBA;MAmBAC;QACAvB,eADA;QAEAwB,sBAFA;QAGAC,mCAHA;QAIAC,mCAJA;QAKAC;MALA,CAnBA;MA0BAC,YA1BA;MA2BAC;QACAC,oBADA;QAEAC;UACA;QACA;MAJA,CA3BA;MAiCAC,oBAjCA;MAkCAC,kBAlCA;MAmCAC,0BAnCA;MAoCAC,oBApCA;MAqCAC;IArCA;EAuCA,CA/CA;;EAgDA;IACA;MACA;MACA;MACA,mBACA,iDADA,EAEA,mDAFA;MAIA;MACA;MACA;QACA,6BACAC,uDADA,EAEAA,uDAFA;MAIA,CALA;IAMA,CAfA,CAeA;MACAC;MACA;IACA,CAlBA,SAkBA;MACA;IACA;EACA,CAtEA;;EAuEAC;IACAC;EACA,CAzEA;;EA0EAC;IACA;IACAC;MACA;MACA;MACA;MACA;;MACA;QACAC;QACAC;MACA;;MACA;IACA,CAZA;;IAcA;IACAC;MACA;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;MAJA;IAMA,CAtBA;;IAwBA;IACAC;MACA;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;;QACA;UAAA;MAJA;IAMA,CAhCA;;IAkCA;MACA;QACA;MACA,CAFA,CAEA;QACAR;QACA;QACA;MACA;IACA,CA1CA;;IA4CAS;MACA;IACA,CA9CA;;IAgDAC;MACA;IACA,CAlDA;;IAoDAC;MACA;MACA;IACA,CAvDA;;IAyDAC;MACA;MACA;IACA,CA5DA;;IA8DAC;MACA;MACA;IACA,CAjEA;;IAmEAC;MACA;QACA7C,YADA;QAEAC,YAFA;QAGAC,SAHA;QAIAC,WAJA;QAKAC,WALA;QAMAC,aANA;QAOAC;MAPA;MASA;IACA,CA9EA;;IAgFA;MACA;QACA;UACAwC,4BADA;UAEAC,mCAFA;UAGAC,kCAHA;UAIAC;QAJA;QAMA;;QACA;UACA;UACA;QACA,CAHA,MAGA;UACA;QACA;MACA,CAdA,CAcA;QACA;UACAlB;UACA;QACA;MACA;IACA,CArGA;;IAuGA;MACA;;MACA;QACA;;QACA;UACA;UACA;QACA,CAHA,MAGA;UACA;QACA;MACA,CARA,CAQA;QACAA;QACA;QACA;MACA,CAZA,SAYA;QACA;MACA;IACA,CAxHA;;IA0HA;MACA;;MACA;QACA;;QACA;UACA;QACA,CAFA,MAEA;UACA;QACA;MACA,CAPA,CAOA;QACAA;QACA;QACA;MACA,CAXA,SAWA;QACA;MACA;IACA,CA1IA;;IA4IAmB;MACA;MACA;QAAAlD;QAAAC;MAAA;MACA;QACAD,SADA;QAEAC,QAFA;QAGAC,cAHA;QAIAC,WAJA;QAKAC,WALA;QAMAC,aANA;QAOAC;MAPA;MASA;IACA,CAzJA;;IA2JA6C;MACA;MAAA;MACA;MACA;QACA;MACA,CAFA;IAGA,CAjKA;;IAmKAC;MACA;QAAAC;MAAA;MACA;MACA;QACA;MACA,CAFA;IAGA,CAzKA;;IA2KA;MACA;QACA;UACAP,4BADA;UAEAC,oBAFA;UAGAC,kCAHA;UAIAC;QAJA;QAMA;;QACA;UACA;UACA;QACA,CAHA,MAGA;UACA;QACA;MACA,CAdA,CAcA;QACA;UACAlB;UACA;QACA;MACA;IACA,CAhMA;;IAkMA;IACAuB;MACA;IACA,CArMA;;IAuMA;IACAC;MACA;IACA,CA1MA;;IA4MA;IACA;MACA;QACA,+CADA,CAEA;;QACA;UAAAC;QAAA,GAHA,CAIA;;QACA;QACAC;QACAA;QACAA;QACAxB;MACA,CAVA,CAUA;QACAF;QACA;MACA;IACA,CA5NA;;IA6NA;IACA2B;MACAC;;MACA;QACAA;;QACA;UACA;QACA;MACA;IACA;;EAtOA;AA1EA", "names": ["delSopDoc", "getSopDocList", "downloadSopDoc", "name", "components", "FormDialog", "SopDirFormDialog", "SplitPane", "data", "searchForm", "pageIndex", "pageSize", "dirId", "doc<PERSON>ame", "docCode", "doc<PERSON><PERSON>us", "deleted", "total", "tableData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tableName", "tableLoading", "treeLoading", "initLoading", "tableOption", "mainH", "buttonOption", "serveIp", "uploadUrl", "exportUrl", "DownLoadUrl", "treeData", "defaultProps", "children", "label", "docStatusOptions", "deletedOptions", "sopDirDialogVisible", "sopDirDialogForm", "sopDirFormLoading", "document", "console", "<PERSON><PERSON><PERSON><PERSON>", "window", "methods", "formatFileSize", "fileSize", "index", "getStatusType", "formatStatus", "getZHHans", "showDialog", "handleCurrentChange", "handleSizeChange", "getSearchBtn", "resetForm", "title", "message", "confirmText", "cancelText", "handleNodeClick", "showSopDirDialog", "addSopDirChild", "parentId", "expandAll", "collapseAll", "type", "link", "expandNodes", "node"], "sourceRoot": "src/views/SOP/sopDoc", "sources": ["index.vue"], "sourcesContent": ["<template>\n  <div class=\"root usemystyle\">\n    <div class=\"root-layout\" v-loading=\"initLoading\">\n      <split-pane \n        :min-percent=\"15\" \n        :max-percent=\"40\" \n        :default-percent=\"20\" \n        split=\"vertical\">\n        <template slot=\"pane1\">\n          <div class=\"root-left\">\n            <div class=\"tree-toolbar\">\n              <el-button-group>\n                <el-button \n                  size=\"small\" \n                  type=\"primary\"\n                  icon=\"el-icon-plus\"\n                  @click=\"addSopDirChild({})\">新建</el-button>\n                <el-button \n                  size=\"small\"\n                  icon=\"el-icon-refresh\"\n                  @click=\"getDirTree\">刷新</el-button>\n                <el-button \n                  size=\"small\"\n                  @click=\"expandAll\">展开</el-button>\n                <el-button \n                  size=\"small\"\n                  @click=\"collapseAll\">收起</el-button>\n              </el-button-group>\n            </div>\n            <el-tree\n              ref=\"tree\"\n              :data=\"treeData\"\n              :props=\"defaultProps\"\n              highlight-current\n              @node-click=\"handleNodeClick\"\n              v-loading=\"treeLoading\">\n              <span class=\"custom-tree-node\" slot-scope=\"{ node, data }\">\n                <div style=\"line-height: 22px;\">\n                  <div class=\"tree-title\">{{ node.data.name }}</div>\n                </div>\n                <span class=\"tree-node-actions\">\n                  <el-button type=\"text\" size=\"mini\" @click.stop=\"() => showSopDirDialog(data)\">\n                    <i class=\"el-icon-edit\"></i>\n                  </el-button>\n                  <el-button type=\"text\" size=\"mini\" @click.stop=\"() => addSopDirChild(data)\">\n                    <i class=\"el-icon-plus\"></i>\n                  </el-button>\n                  <el-button type=\"text\" size=\"mini\" @click.stop=\"() => deleteSopDir(data)\">\n                    <i class=\"el-icon-delete\"></i>\n                  </el-button>\n                </span>\n              </span>\n            </el-tree>\n          </div>\n        </template>\n        <template slot=\"pane2\">\n          <div class=\"root-right\">\n            <div class=\"InventorySearchBox\">\n              <div class=\"search-form\">\n                <el-form size=\"small\" :inline=\"true\" ref=\"form\" :model=\"searchForm\" @submit.native.prevent>\n                  <div class=\"form-content\">\n                    <div class=\"search-area\">\n                      <div class=\"search-row\">\n                            <el-form-item label=\"名称\" prop=\"docName\" label-width=\"40px\" class=\"search-form-item\">\n                              <el-input v-model=\"searchForm.docName\" placeholder=\"输入名称\" clearable size=\"small\">                            \n                              </el-input>\n                            </el-form-item>\n                            <el-form-item label=\"编码\" prop=\"docCode\" label-width=\"40px\" class=\"search-form-item\">\n                              <el-input v-model=\"searchForm.docCode\" placeholder=\"输入编码\" clearable size=\"small\"></el-input>\n                            </el-form-item>\n                            <el-form-item label=\"版本\" prop=\"docVersion\" label-width=\"40px\" class=\"search-form-item\">\n                              <el-input v-model=\"searchForm.docVersion\" placeholder=\"输入版本\" clearable size=\"small\"></el-input>\n                            </el-form-item>\n                            <el-form-item label=\"状态\" prop=\"docStatus\" label-width=\"40px\" class=\"search-form-item\">\n                              <el-select v-model=\"searchForm.docStatus\" placeholder=\"选择\" clearable size=\"small\">\n                                <el-option label=\"有效\" :value=\"1\"></el-option>\n                                <el-option label=\"无效\" :value=\"0\"></el-option>\n                              </el-select>\n                            </el-form-item>\n                        <div class=\"action-buttons\">\n                          <el-button type=\"success\" icon=\"el-icon-circle-plus-outline\" @click=\"showDialog({})\" size=\"small\">新增</el-button>\n                          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"getSearchBtn\" size=\"small\">查询</el-button>\n                          <el-button icon=\"el-icon-refresh\" @click=\"resetForm\" size=\"small\">重置</el-button>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </el-form>\n              </div>\n            </div>\n            <div class=\"root-main\">\n              <el-table class=\"mt-3\"\n                        :height=\"700\"\n                        border\n                        v-loading=\"tableLoading\"\n                        :data=\"tableData\"\n                        style=\"width: 100%; border-radius: 4px;\"\n                        :empty-text=\"'暂无数据'\">\n                <el-table-column\n                  type=\"index\"\n                  label=\"序号\"\n                  width=\"50\"\n                  align=\"center\">\n                </el-table-column>\n                <el-table-column v-for=\"(item) in tableName\"\n                                 :default-sort=\"{prop: item.value, order: 'descending'}\"\n                                 :key=\"item.value\"\n                                 :prop=\"item.value\"\n                                 :label=\"typeof item.text === 'function' ? item.text() : item.text\"\n                                 :width=\"item.width\"\n                                 :align=\"item.alignType || 'center'\"\n                                 sortable\n                                 show-overflow-tooltip>\n                  <template slot-scope=\"scope\">\n                    <template v-if=\"item.value === 'FileSize'\">\n                      {{ formatFileSize(scope.row[item.value]) }}\n                    </template>\n                    <template v-else-if=\"item.value === 'DocStatus'\">\n                      <el-tag :type=\"getStatusType(scope.row[item.value])\" size=\"small\">\n                        {{ formatStatus(scope.row[item.value]) }}\n                      </el-tag>\n                    </template>\n                    <template v-else>\n                      {{ scope.row[item.value] }}\n                    </template>\n                  </template>\n                </el-table-column>\n                <el-table-column prop=\"operation\" :min-width=\"160\" :label=\"$t('GLOBAL._ACTIONS')\" align=\"center\">\n                  <template slot-scope=\"scope\">\n                    <el-button size=\"mini\" type=\"text\" @click=\"showDialog(scope.row)\">{{ $t('GLOBAL._CK') }}</el-button>\n                    <el-button size=\"mini\" type=\"text\" @click=\"handleDownload(scope.row)\">{{ $t('GLOBAL.Download') }}</el-button>\n                    <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._SC') }}</el-button>\n                    <!-- <el-button size=\"mini\" type=\"text\" @click=\"delRow(scope.row)\">{{ $t('GLOBAL._shenqing') }}</el-button>                -->\n                  </template>\n                </el-table-column>\n              </el-table>\n            </div>\n          </div>\n        </template>\n      </split-pane>\n      <div class=\"root-footer\">\n        <el-pagination\n            class=\"mt-3\"\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"searchForm.pageIndex\"\n            :page-sizes=\"[10,20, 50, 100,500]\"\n            :page-size=\"searchForm.pageSize\"\n            layout=\"->,total, sizes, prev, pager, next, jumper\"\n            :total=\"total\"\n            background\n        ></el-pagination>\n      </div>\n      <form-dialog @saveForm=\"getSearchBtn\" ref=\"formDialog\" :treeData=\"treeData\"></form-dialog>\n      <sop-dir-form-dialog \n        :visible.sync=\"sopDirDialogVisible\" \n        :form-data=\"sopDirDialogForm\" \n        @saveForm=\"getDirTree\" \n        ref=\"sopDirFormDialog\">\n      </sop-dir-form-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport '@/views/Inventory/mystyle.scss'\nimport FormDialog from './form-dialog'\nimport TreeSelect from '../components/tree-select'\nimport SplitPane from '../components/split-pane'\nimport {\n    delSopDoc, getSopDocList, downloadSopDoc\n} from \"@/api/SOP/sopDoc\";\nimport { getSopDirTree, saveSopDirForm, delSopDir } from \"@/api/SOP/sopDir\";\nimport SopDirFormDialog from \"@/views/SOP/sopDir/form-dialog\";\nimport { sopDocColumns } from '@/columns/SOP/sopDoc.js';\n\nexport default {\n  name: 'index.vue',\n  components: {\n    FormDialog,\n    SopDirFormDialog,\n    SplitPane\n  },\n  data() {\n    return {\n      searchForm: {\n        pageIndex: 1,\n        pageSize: 20,\n        dirId: '',\n        docName: '',\n        docCode: '',\n        docStatus: '',\n        deleted: ''\n      },\n      total: 0,\n      tableData: [],\n      hansObj: this.$t('SOP文档主表.table'),\n      tableName: [],\n      tableLoading: false,\n      treeLoading: false,\n      initLoading: false,\n      tableOption: [],\n      mainH: 0,\n      buttonOption: {\n        name:'SOP文档主表',\n        serveIp:'baseURL_DFM',\n        uploadUrl:'/api/SopDoc/ImportData',\n        exportUrl:'/api/SopDoc/ExportData',\n        DownLoadUrl:'/api/SopDoc/DownLoadTemplate',\n      },\n      treeData: [],\n      defaultProps: {\n        children: 'children',\n        label:  function (node){\n          return node.data.DirName\n        } \n      },\n      docStatusOptions: [],\n      deletedOptions: [],\n      sopDirDialogVisible: false,\n      sopDirDialogForm: {},\n      sopDirFormLoading: false\n    }\n  },\n  async mounted() {\n    try {\n      this.initLoading = true\n      this.getZHHans()\n      await Promise.all([\n        this.getDictData('docStatus', 'docStatusOptions'),\n        this.getDictData('deletedStatus', 'deletedOptions')\n      ])\n      await this.getDirTree()\n      await this.getTableData()\n      this.$nextTick(() => {\n        this.mainH = this.$webHeight(\n          document.getElementsByClassName('root')[0].clientHeight,\n          document.getElementsByClassName('root')[0].clientHeight\n        )\n      })\n    } catch (err) {\n      console.error('页面初始化失败:', err)\n      this.$message.error('页面初始化失败，请刷新重试')\n    } finally {\n      this.initLoading = false\n    }\n  },\n  beforeDestroy() {\n    window.onresize = null\n  },\n  methods: {\n    // 格式化文件大小\n    formatFileSize(size) {\n      if (!size) return '0 B'\n      const units = ['B', 'KB', 'MB', 'GB', 'TB']\n      let index = 0\n      let fileSize = parseFloat(size)\n      while (fileSize >= 1024 && index < units.length - 1) {\n        fileSize /= 1024\n        index++\n      }\n      return `${fileSize.toFixed(2)} ${units[index]}`\n    },\n\n    // 获取状态对应的类型\n    getStatusType(status) {\n      switch (status) {\n        case 1: return 'success'\n        case 2: return 'warning'\n        case 0: return 'info'\n        default: return ''\n      }\n    },\n\n    // 格式化状态\n    formatStatus(status) {\n      switch (status) {\n        case 1: return this.$t('SOP.StatusValid')\n        case 2: return this.$t('SOP.AuditPending')\n        case 0: return this.$t('SOP.StatusInvalid')\n        default: return this.$t('GLOBAL._WZ')\n      }\n    },\n\n    async getDictData(dictType, targetKey) {\n      try {\n        this[targetKey] = await this.$getNewDataDictionary(dictType)\n      } catch (err) {\n        console.error(`获取${dictType}字典数据失败:`, err)\n        this.$message.error('获取字典数据失败')\n        throw err\n      }\n    },\n\n    getZHHans() {\n      this.tableName = sopDocColumns\n    },\n\n    showDialog(row) {\n      this.$refs.formDialog.show(row)\n    },\n\n    handleCurrentChange(page) {\n      this.searchForm.pageIndex = page\n      this.getTableData()\n    },\n\n    handleSizeChange(size) {\n      this.searchForm.pageSize = size\n      this.getTableData()\n    },\n\n    getSearchBtn() {\n      this.searchForm.pageIndex = 1\n      this.getTableData()\n    },\n\n    resetForm() {\n      this.searchForm = {\n        pageIndex: 1,\n        pageSize: 20,\n        dirId: '',\n        docName: '',\n        docCode: '',\n        docStatus: '',\n        deleted: ''\n      }\n      this.getTableData()\n    },\n\n    async delRow(row) {\n      try {\n        await this.$confirms({\n          title: this.$t('GLOBAL._TS'),\n          message: this.$t('GLOBAL._COMFIRM'),\n          confirmText: this.$t('GLOBAL._QD'),\n          cancelText: this.$t('GLOBAL._QX')\n        })\n        const res = await delSopDoc([row.ID])\n        if (res.success) {\n          this.$message.success(res.msg || '删除成功')\n          this.getTableData()\n        } else {\n          this.$message.error(res.msg || '删除失败')\n        }\n      } catch (err) {\n        if (err) {\n          console.error('删除数据失败:', err)\n          this.$message.error('删除失败')\n        }\n      }\n    },\n\n    async getTableData(data) {\n      this.tableLoading = true\n      try {\n        const res = await getSopDocList(this.searchForm)\n        if (res.success) {\n          this.tableData = res.response.data || []\n          this.total = res.response.dataCount || 0\n        } else {\n          this.$message.error(res.msg || '获取数据失败')\n        }\n      } catch (err) {\n        console.error('获取表格数据失败:', err)\n        this.$message.error('获取数据失败')\n        throw err\n      } finally {\n        this.tableLoading = false\n      }\n    },\n    \n    async getDirTree() {\n      this.treeLoading = true\n      try {\n        const res = await getSopDirTree()\n        if (res.success) {\n          this.treeData = res.response || []\n        } else {\n          this.$message.error(res.msg || '获取目录树失败')\n        }\n      } catch (err) {\n        console.error('获取目录树失败:', err)\n        this.$message.error('获取目录树失败')\n        throw err\n      } finally {\n        this.treeLoading = false\n      }\n    },\n    \n    handleNodeClick(data) {\n      // 保留分页配置,重置其他搜索条件\n      const { pageIndex, pageSize } = this.searchForm\n      this.searchForm = {\n        pageIndex,\n        pageSize,\n        dirId: data.id,\n        docName: '',\n        docCode: '',\n        docStatus: '',\n        deleted: ''\n      }\n      this.getTableData()\n    },\n    \n    showSopDirDialog(data) {\n      this.sopDirDialogForm = { ...data }\n      this.sopDirDialogVisible = true\n      this.$nextTick(() => {\n        this.$refs.sopDirFormDialog.show(data, 'show')\n      })\n    },\n    \n    addSopDirChild(data) {\n      this.sopDirDialogForm = { parentId: data.id }\n      this.sopDirDialogVisible = true\n      this.$nextTick(() => {\n        this.$refs.sopDirFormDialog.show(data, 'add')\n      })\n    },\n    \n    async deleteSopDir(data) {\n      try {\n        await this.$confirms({\n          title: this.$t('GLOBAL._TS'),\n          message: '确定删除该目录吗？',\n          confirmText: this.$t('GLOBAL._QD'),\n          cancelText: this.$t('GLOBAL._QX')\n        })\n        const res = await delSopDir([data.id])\n        if (res.success) {\n          this.$message.success(res.msg || '删除成功')\n          await this.getDirTree()\n        } else {\n          this.$message.error(res.msg || '删除失败')\n        }\n      } catch (err) {\n        if (err) {\n          console.error('删除目录失败:', err)\n          this.$message.error('删除失败')\n        }\n      }\n    },\n\n    // 展开所有节点\n    expandAll() {\n      this.expandNodes(this.$refs.tree.store.root, true)          \n    },\n\n    // 收起所有节点\n    collapseAll() {\n      this.expandNodes(this.$refs.tree.store.root, false)\n    },\n\n    // 处理文件下载\n    async handleDownload(row) {\n      try {\n        const res = await downloadSopDoc(row.FileUuid)\n        // 创建下载链接\n        const blob = new Blob([res], { type: res.type })\n        // const fileName = row.DocName.split('.').slice(0, -1).join('.') // 移除最后一个扩展名\n        const link = document.createElement('a')\n        link.href = window.URL.createObjectURL(blob)\n        link.download = row.DocName\n        link.click()\n        window.URL.revokeObjectURL(link.href)\n      } catch (err) {\n        console.error('文件下载失败:', err)\n        this.$message.error('文件下载失败')\n      }\n    },\n    //树节点展开关闭\n    expandNodes(node, type){\n      node.expanded = type;\n      for(let i = 0; i<node.childNodes.length; i++){\n        node.childNodes[i].expanded = type;\n        if(node.childNodes[i].childNodes.length > 0){\n          this.expandNodes(node.childNodes[i], type);\n        }\n      }\n    },\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.root-layout {\n  height: calc(100% - 60px);\n}\n.search-form-item {\n  .el-form-item__content {\n    width: 180px; // 统一宽度为180px\n  }\n}\n.root-left {\n  height: 100%;\n  padding: 10px;\n  overflow: auto;\n  background-color: #f5f7fa;\n  border-radius: 4px;\n\n  .tree-toolbar {\n    margin-bottom: 10px;\n    display: flex;\n    justify-content: space-between;\n  }\n\n  .custom-tree-node {\n    flex: 1;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    font-size: 14px;\n    padding-right: 8px;\n    width: 100%;\n    .tree-title {\n      font-weight: 500;\n    }\n  }\n\n  .tree-node-actions {\n    display: none;\n  }\n\n  .el-tree-node__content:hover {\n    .tree-node-actions {\n      display: inline-block;\n    }\n  }\n}\n\n.root-right {\n  padding: 0 12px;\n  height: 100%;\n  overflow: auto;\n\n  .InventorySearchBox {\n    background: #fff;\n    padding: 8px;\n    border-radius: 4px;\n    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n    margin-bottom: 8px;\n\n    .search-form {\n      :deep(.el-form) {\n        .el-form-item--small.el-form-item {\n          margin-bottom: 0;\n        }\n      }\n\n      .form-content {\n        padding: 4px;\n        \n        .search-area {\n          .search-row {\n            display: flex;\n            align-items: center;\n            gap: 4px;\n\n            .el-form-item {\n              margin: 0;\n              flex: none;\n              \n              .el-form-item__label {\n                padding-right: 4px;\n                line-height: 26px;\n                font-size: 12px;\n              }\n\n              .el-form-item__content {\n                line-height: 26px;\n\n                .el-input,\n                .el-select {\n                  :deep(.el-input__inner) {\n                    height: 26px;\n                    line-height: 26px;\n                    padding: 0 8px;\n                    font-size: 12px;\n                  }\n\n                  :deep(.el-input-group__append) {\n                    padding: 0;\n                    .el-button {\n                      padding: 0 10px;\n                      height: 26px;\n                      border: none;\n                    }\n                  }\n                }\n              }\n            }\n\n            .el-button {\n              height: 26px;\n              padding: 0 10px;\n              font-size: 12px;\n              margin-left: 4px;\n              \n              [class^=\"el-icon-\"] {\n                margin-right: 3px;\n                font-size: 12px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .root-main {\n    margin-top: 12px;\n\n    .el-table {\n      border-radius: 4px;\n      overflow: hidden;\n    }\n  }\n}\n</style>"]}]}